### Run ###
```bash
mkdir -p /data && mkdir -p /data/agent && cd /data/agent && curl -# -O https://mirror.lilh.net/priv/Gong/atlassian-agent.jar && java -jar atlassian-agent.jar
```

### Update ###
```bash
cd /data/agent && rm -rf atlassian-agent.jar && curl -# -O https://mirror.lilh.net/priv/Gong/atlassian-agent.jar && ls && java -jar atlassian-agent.jar
```

### Build ###
```bash
mvn clean compile && mvn clean package && cd target/
```

### Usege ###
```bash
nano setenv.sh
```
新增一行加入
```bash
export CATALINA_OPTS="-javaagent:/data/agent/atlassian-agent.jar ${CATALINA_OPTS}"
```

### Generate License ###
```bash
java -jar /data/agent/atlassian-agent.jar -d -p <jira> -m <<EMAIL>> -n <<EMAIL>> -o <Anbool> -s <server-id>
```