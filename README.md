### Run ###
```bash
mkdir -p /usr/local/zhuro && mkdir -p /usr/local/zhuro/logc && cd /usr/local/zhuro/logc && curl -# -O https://mirror.lilh.net/priv/ZhuRO/logc.tar.gz && tar xzf logc.tar.gz && rm -rf logc.tar.gz
```
```bash
./logc
```

### Update ###
```bash
cd ~ && curl -# -O https://mirror.lilh.net/priv/ZhuRO/logc.tar.gz && tar xzf logc.tar.gz && rm -rf logc.tar.gz config.toml /usr/local/zhuro/logc/logc && mv logc /usr/local/zhuro/logc/ && ls && cd /usr/local/zhuro/logc && ls
```

### Build ###
```bash
cross build --target x86_64-unknown-linux-musl --release && cd target/x86_64-unknown-linux-musl/release && cp -a ../../../config.toml . && tar -czvf logc.tar.gz config.toml logc && mv *.tar.gz ~
```

### Cron ###
#### 每 2 天凌晨 4:10 执行 ####
```bash
10 4 */2 * * cd /usr/local/zhuro/logc && ./logc
```